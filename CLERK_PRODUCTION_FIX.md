# 🔧 Fix Clerk "Browser Unauthenticated" Error

## 🚨 Error Details
```json
{
    "message": "<PERSON>rowser unauthenticated",
    "code": "dev_browser_unauthenticated"
}
```

This error occurs when using Clerk development keys in production or missing domain configuration.

## ✅ **Step-by-Step Fix**

### 1. **Check Your Current Setup**
After deploying, open browser console on your deployed site to see:
- What type of key you're using (DEVELOPMENT vs PRODUCTION)
- Your current domain

### 2. **Configure Clerk Dashboard**

#### A. Add Your Domain
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Select your application
3. Go to **"Domains"** section
4. Click **"Add domain"**
5. Add your Netlify URL: `https://your-app-name.netlify.app`
6. Set it as **Production** domain

#### B. Get Production Keys (Recommended)
1. In Clerk Dashboard, go to **"API Keys"**
2. Switch to **"Production"** tab
3. Copy the **Publishable key** (starts with `pk_live_...`)

#### C. Configure JWT Template (For Supabase)
1. Go to **"JWT Templates"**
2. Create or verify **"supabase"** template exists
3. This is required for your database operations

### 3. **Update Netlify Environment Variables**

1. Go to **Netlify Dashboard**
2. **Site settings → Environment variables**
3. Update/Add:
   ```
   VITE_CLERK_PUBLISHABLE_KEY = pk_live_your_production_key_here
   VITE_SUPABASE_URL = https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY = your_supabase_anon_key
   ```

### 4. **Alternative: Keep Development Key**
If you prefer to keep using development keys:

1. In Clerk Dashboard → **"Domains"**
2. Add your Netlify domain to **development** environment
3. Ensure the domain is properly configured

### 5. **Deploy and Test**

1. **Trigger new deployment** (push a change or manual deploy)
2. **Clear browser cache** for your deployed site
3. **Test authentication flow**

## 🔍 **Debugging Steps**

### Check Console Logs
After deployment, check browser console for:
```
Clerk Key Type: DEVELOPMENT or PRODUCTION
Current Domain: https://your-app-name.netlify.app
```

### Verify Environment Variables
In Netlify build logs, you should see your environment variables being used (values will be hidden for security).

### Test Authentication Flow
1. Try to sign in/sign up
2. Check if redirects work properly
3. Verify protected routes work

## 🚨 **Common Issues & Solutions**

### Issue 1: Still getting dev_browser_unauthenticated
**Solution**: 
- Ensure your Netlify domain is added to Clerk domains
- Use production keys for production deployment
- Clear browser cache completely

### Issue 2: Authentication works but database fails
**Solution**:
- Check Supabase environment variables
- Verify JWT template in Clerk is configured
- Check Supabase RLS policies

### Issue 3: Redirects not working
**Solution**:
- Verify `netlify.toml` has SPA redirect rules
- Check Clerk redirect URLs in dashboard

## 🧹 **Cleanup After Fix**

Once everything works, remove the debug code from `src/main.jsx`:

```javascript
// Remove these lines after fixing:
console.log('Clerk Key Type:', PUBLISHABLE_KEY?.startsWith('pk_test_') ? 'DEVELOPMENT' : 'PRODUCTION')
console.log('Current Domain:', window.location.origin)
```

## 📞 **Still Need Help?**

If the issue persists:
1. Check Netlify build logs for environment variable issues
2. Verify all three environment variables are set correctly
3. Ensure Clerk dashboard configuration matches your deployment domain
4. Contact Clerk support with your trace ID: `87b7340a6e7d59fbb54889b6d6453802`

**The key is ensuring your production domain is properly configured in Clerk and you're using the right keys for your environment!**
