import { StrictMode } from 'react'
  import { createRoot } from 'react-dom/client'
  import './index.css'
  import App from './App.jsx'
  import { ClerkProvider } from '@clerk/clerk-react'
  import { dark, neobrutalism, shadesOfPurple } from '@clerk/themes'

  // Import your Publishable Key
  const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

  if (!PUBLISHABLE_KEY) {
    throw new Error('Add your Clerk Publishable Key to the .env file')
  }

  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <ClerkProvider
      appearance={
        {
          baseTheme:shadesOfPurple,
        }
      }
       publishableKey={PUBLISHABLE_KEY}>
        <App />
      </ClerkProvider>
    </StrictMode>,
  )